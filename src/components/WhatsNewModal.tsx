import React, { useState, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, Play } from 'lucide-react';

interface WhatsNewItem {
    id: string;
    title: string;
    description: string;
    videoUrl?: string;
    imageUrl?: string;
    version?: string;
    date?: string;
}

interface WhatsNewModalProps {
    isOpen: boolean;
    onClose: () => void;
    isDarkMode: boolean;
}

// 示例数据 - 实际使用时可以从API获取
const whatsNewData: WhatsNewItem[] = [
    {
        id: '1',
        title: 'AI智能图片编辑',
        description: '全新推出的AI图片编辑功能，让您可以通过自然语言描述来修改图片。支持上传自己的图片进行编辑，只需点击图片，输入您想要的修改描述，AI就能智能理解并生成符合要求的新图片。支持风格转换、对象替换、场景修改、背景更换等多种编辑方式，让图片创作变得更加简单直观。',
        videoUrl: require('../static/update/image_change.mp4'),
        imageUrl: 'https://via.placeholder.com/600x300/4F46E5/FFFFFF?text=AI+图片编辑',
        date: '2025-07-18'
    },
    {
        id: '2',
        title: '智能文档分析',
        description: '新增强大的文档分析功能，支持PDF、Word、Excel等多种格式。AI可以快速理解文档内容，提供智能摘要和问答服务。',
        imageUrl: 'https://via.placeholder.com/600x300/059669/FFFFFF?text=Document+Analysis',
        date: '2025-07-10'
    },
    {
        id: '3',
        title: '多模态图像理解',
        description: '升级了图像识别能力，现在可以理解图片内容、分析图表数据、识别文字，并基于图像内容进行深度对话。',
        imageUrl: 'https://via.placeholder.com/600x300/DC2626/FFFFFF?text=Image+Understanding',
        date: '2025-07-05'
    }
];

const WhatsNewModal: React.FC<WhatsNewModalProps> = ({ isOpen, onClose, isDarkMode }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isVideoPlaying, setIsVideoPlaying] = useState(false);

    const currentItem = whatsNewData[currentIndex];

    // 重置状态当模态框打开时
    useEffect(() => {
        if (isOpen) {
            setCurrentIndex(0);
            // 如果第一个项目有视频，自动开始播放
            setIsVideoPlaying(!!whatsNewData[0]?.videoUrl);
        }
    }, [isOpen]);

    // 当切换到有视频的项目时，自动播放视频
    useEffect(() => {
        if (currentItem?.videoUrl) {
            setIsVideoPlaying(true);
        } else {
            setIsVideoPlaying(false);
        }
    }, [currentIndex, currentItem]);

    // ESC键关闭
    useEffect(() => {
        const handleEscape = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscape);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    const handlePrevious = () => {
        setCurrentIndex((prev) => (prev > 0 ? prev - 1 : whatsNewData.length - 1));
    };

    const handleNext = () => {
        setCurrentIndex((prev) => (prev < whatsNewData.length - 1 ? prev + 1 : 0));
    };

    const toggleVideo = () => {
        setIsVideoPlaying(!isVideoPlaying);
    };

    if (!isOpen) return null;

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-900',
        textSecondary: isDarkMode ? 'text-gray-300' : 'text-gray-600',
        textMuted: isDarkMode ? 'text-gray-400' : 'text-gray-500',
        border: isDarkMode ? 'border-gray-700' : 'border-gray-200',
        buttonHover: isDarkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100',
        closeButton: isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700',
    };

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            {/* 背景遮罩 */}
            <div
                className="absolute inset-0 bg-black/50 transition-opacity duration-300"
                onClick={onClose}
            />

            {/* 模态框内容 */}
            <div
                className={`relative w-full max-w-2xl ${themeClasses.background} rounded-xl shadow-2xl transform transition-all duration-300 scale-100 opacity-100`}
                onClick={(e) => e.stopPropagation()}
            >
                {/* 头部 */}
                <div className={`flex items-center justify-between p-6 border-b ${themeClasses.border}`}>
                    <div>
                        <h2 className={`text-2xl font-bold ${themeClasses.text}`}>
                            What's New
                        </h2>
                        <p className={`text-sm ${themeClasses.textMuted} mt-1`}>
                            {currentIndex + 1} of {whatsNewData.length}
                        </p>
                    </div>
                    <button
                        onClick={onClose}
                        className={`p-2 rounded-lg transition-colors ${themeClasses.closeButton} ${themeClasses.buttonHover}`}
                    >
                        <X size={24} />
                    </button>
                </div>

                {/* 内容区域 */}
                <div className="p-6">
                    {/* 功能标题和日期信息 */}
                    <div className="mb-4">
                        <h3 className={`text-xl font-semibold ${themeClasses.text} mb-2`}>
                            {currentItem.title}
                        </h3>
                        {currentItem.date && (
                            <p className={`text-sm ${themeClasses.textMuted}`}>
                                发布日期: {currentItem.date}
                            </p>
                        )}
                    </div>

                    {/* 媒体内容 */}
                    <div className="mb-6">
                        {currentItem.videoUrl ? (
                            <div className="relative rounded-lg overflow-hidden bg-gray-100">
                                <div className="aspect-video flex items-center justify-center">
                                    {isVideoPlaying ? (
                                        <video
                                            className="w-full h-full object-cover"
                                            controls
                                            autoPlay
                                            loop
                                            muted
                                            playsInline
                                            src={currentItem.videoUrl}
                                            onLoadedData={(e) => {
                                                const video = e.target as HTMLVideoElement;
                                                video.play().catch(console.error);
                                            }}
                                        />
                                    ) : (
                                        <button
                                            className="relative w-full h-full cursor-pointer group"
                                            onClick={toggleVideo}
                                            aria-label="播放视频"
                                        >
                                            <img
                                                src={currentItem.imageUrl ?? 'https://via.placeholder.com/600x300/6B7280/FFFFFF?text=点击播放视频'}
                                                alt={currentItem.title}
                                                className="w-full h-full object-cover"
                                            />
                                            <div className="absolute inset-0 bg-black/30 flex items-center justify-center group-hover:bg-black/40 transition-colors">
                                                <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center group-hover:bg-white transition-colors">
                                                    <Play size={24} className="text-gray-800 ml-1" />
                                                </div>
                                            </div>
                                        </button>
                                    )}
                                </div>
                            </div>
                        ) : (
                            <div className="rounded-lg overflow-hidden">
                                <img
                                    src={currentItem.imageUrl}
                                    alt={currentItem.title}
                                    className="w-full aspect-video object-cover"
                                />
                            </div>
                        )}
                    </div>

                    {/* 描述文本 */}
                    <div className="mb-6">
                        <p className={`text-base leading-relaxed ${themeClasses.textSecondary}`}>
                            {currentItem.description}
                        </p>
                    </div>
                </div>

                {/* 底部导航 */}
                <div className={`flex items-center justify-between p-6 border-t ${themeClasses.border}`}>
                    <button
                        onClick={handlePrevious}
                        className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${themeClasses.buttonHover} ${themeClasses.textSecondary}`}
                    >
                        <ChevronLeft size={20} />
                        <span>上一个</span>
                    </button>

                    {/* 指示器 */}
                    <div className="flex space-x-2">
                        {whatsNewData.map((item, index) => {
                            const isActive = index === currentIndex;
                            const activeColor = isDarkMode ? 'bg-blue-400' : 'bg-blue-600';
                            const inactiveColor = isDarkMode ? 'bg-gray-600' : 'bg-gray-300';

                            return (
                                <button
                                    key={item.id}
                                    onClick={() => {
                                        setCurrentIndex(index);
                                    }}
                                    className={`w-2 h-2 rounded-full transition-colors ${isActive ? activeColor : inactiveColor
                                        }`}
                                    aria-label={`切换到第 ${index + 1} 个功能`}
                                />
                            );
                        })}
                    </div>

                    <button
                        onClick={handleNext}
                        className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${themeClasses.buttonHover} ${themeClasses.textSecondary}`}
                    >
                        <span>下一个</span>
                        <ChevronRight size={20} />
                    </button>
                </div>
            </div>
        </div>
    );
};

export default WhatsNewModal;
