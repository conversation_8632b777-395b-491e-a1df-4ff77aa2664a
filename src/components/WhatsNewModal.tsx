import React, { useState, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, Play } from 'lucide-react';

interface WhatsNewItem {
    id: string;
    title: string;
    description: string;
    videoUrl?: string;
    imageUrl?: string;
    version?: string;
    date?: string;
}

interface WhatsNewModalProps {
    isOpen: boolean;
    onClose: () => void;
    isDarkMode: boolean;
}

// 示例数据 - 实际使用时可以从API获取
const whatsNewData: WhatsNewItem[] = [
    {
        id: '1',
        title: '全新AI对话体验',
        description: '我们重新设计了AI对话界面，提供更流畅的交互体验。新增了实时打字效果、消息状态指示器，以及更智能的上下文理解能力。',
        videoUrl: 'https://example.com/video1.mp4',
        imageUrl: 'https://via.placeholder.com/600x300/4F46E5/FFFFFF?text=AI+Chat+Experience',
        version: 'v2.1.0',
        date: '2024-01-15'
    },
    {
        id: '2',
        title: '智能文档分析',
        description: '新增强大的文档分析功能，支持PDF、Word、Excel等多种格式。AI可以快速理解文档内容，提供智能摘要和问答服务。',
        imageUrl: 'https://via.placeholder.com/600x300/059669/FFFFFF?text=Document+Analysis',
        version: 'v2.0.5',
        date: '2024-01-10'
    },
    {
        id: '3',
        title: '多模态图像理解',
        description: '升级了图像识别能力，现在可以理解图片内容、分析图表数据、识别文字，并基于图像内容进行深度对话。',
        imageUrl: 'https://via.placeholder.com/600x300/DC2626/FFFFFF?text=Image+Understanding',
        version: 'v2.0.0',
        date: '2024-01-05'
    }
];

const WhatsNewModal: React.FC<WhatsNewModalProps> = ({ isOpen, onClose, isDarkMode }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [isVideoPlaying, setIsVideoPlaying] = useState(false);

    const currentItem = whatsNewData[currentIndex];

    // 重置状态当模态框打开时
    useEffect(() => {
        if (isOpen) {
            setCurrentIndex(0);
            setIsVideoPlaying(false);
        }
    }, [isOpen]);

    // ESC键关闭
    useEffect(() => {
        const handleEscape = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscape);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    const handlePrevious = () => {
        setCurrentIndex((prev) => (prev > 0 ? prev - 1 : whatsNewData.length - 1));
        setIsVideoPlaying(false);
    };

    const handleNext = () => {
        setCurrentIndex((prev) => (prev < whatsNewData.length - 1 ? prev + 1 : 0));
        setIsVideoPlaying(false);
    };

    const toggleVideo = () => {
        setIsVideoPlaying(!isVideoPlaying);
    };

    if (!isOpen) return null;

    const themeClasses = {
        background: isDarkMode ? 'bg-gray-900' : 'bg-white',
        text: isDarkMode ? 'text-white' : 'text-gray-900',
        textSecondary: isDarkMode ? 'text-gray-300' : 'text-gray-600',
        textMuted: isDarkMode ? 'text-gray-400' : 'text-gray-500',
        border: isDarkMode ? 'border-gray-700' : 'border-gray-200',
        buttonHover: isDarkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-100',
        closeButton: isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700',
    };

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
            {/* 背景遮罩 */}
            <div
                className="absolute inset-0 bg-black/50 transition-opacity duration-300"
                onClick={onClose}
            />

            {/* 模态框内容 */}
            <div
                className={`relative w-full max-w-2xl ${themeClasses.background} rounded-xl shadow-2xl transform transition-all duration-300 scale-100 opacity-100`}
                onClick={(e) => e.stopPropagation()}
            >
                {/* 头部 */}
                <div className={`flex items-center justify-between p-6 border-b ${themeClasses.border}`}>
                    <div>
                        <h2 className={`text-2xl font-bold ${themeClasses.text}`}>
                            What's New
                        </h2>
                        <p className={`text-sm ${themeClasses.textMuted} mt-1`}>
                            {currentIndex + 1} of {whatsNewData.length}
                        </p>
                    </div>
                    <button
                        onClick={onClose}
                        className={`p-2 rounded-lg transition-colors ${themeClasses.closeButton} ${themeClasses.buttonHover}`}
                    >
                        <X size={24} />
                    </button>
                </div>

                {/* 内容区域 */}
                <div className="p-6">
                    {/* 功能标题和版本信息 */}
                    <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                            <h3 className={`text-xl font-semibold ${themeClasses.text}`}>
                                {currentItem.title}
                            </h3>
                            {currentItem.version && (
                                <span className={`px-2 py-1 text-xs font-medium rounded-full ${isDarkMode ? 'bg-blue-900 text-blue-200' : 'bg-blue-100 text-blue-800'}`}>
                                    {currentItem.version}
                                </span>
                            )}
                        </div>
                        {currentItem.date && (
                            <p className={`text-sm ${themeClasses.textMuted}`}>
                                发布日期: {currentItem.date}
                            </p>
                        )}
                    </div>

                    {/* 媒体内容 */}
                    <div className="mb-6">
                        {currentItem.videoUrl ? (
                            <div className="relative rounded-lg overflow-hidden bg-gray-100">
                                <div className="aspect-video flex items-center justify-center">
                                    {isVideoPlaying ? (
                                        <video
                                            className="w-full h-full object-cover"
                                            controls
                                            autoPlay
                                            src={currentItem.videoUrl}
                                        />
                                    ) : (
                                        <button
                                            className="relative w-full h-full cursor-pointer group"
                                            onClick={toggleVideo}
                                            aria-label="播放视频"
                                        >
                                            <img
                                                src={currentItem.imageUrl ?? 'https://via.placeholder.com/600x300/6B7280/FFFFFF?text=Video+Thumbnail'}
                                                alt={currentItem.title}
                                                className="w-full h-full object-cover"
                                            />
                                            <div className="absolute inset-0 bg-black/30 flex items-center justify-center group-hover:bg-black/40 transition-colors">
                                                <div className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center group-hover:bg-white transition-colors">
                                                    <Play size={24} className="text-gray-800 ml-1" />
                                                </div>
                                            </div>
                                        </button>
                                    )}
                                </div>
                            </div>
                        ) : (
                            <div className="rounded-lg overflow-hidden">
                                <img
                                    src={currentItem.imageUrl}
                                    alt={currentItem.title}
                                    className="w-full aspect-video object-cover"
                                />
                            </div>
                        )}
                    </div>

                    {/* 描述文本 */}
                    <div className="mb-6">
                        <p className={`text-base leading-relaxed ${themeClasses.textSecondary}`}>
                            {currentItem.description}
                        </p>
                    </div>
                </div>

                {/* 底部导航 */}
                <div className={`flex items-center justify-between p-6 border-t ${themeClasses.border}`}>
                    <button
                        onClick={handlePrevious}
                        className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${themeClasses.buttonHover} ${themeClasses.textSecondary}`}
                    >
                        <ChevronLeft size={20} />
                        <span>上一个</span>
                    </button>

                    {/* 指示器 */}
                    <div className="flex space-x-2">
                        {whatsNewData.map((item, index) => {
                            const isActive = index === currentIndex;
                            const activeColor = isDarkMode ? 'bg-blue-400' : 'bg-blue-600';
                            const inactiveColor = isDarkMode ? 'bg-gray-600' : 'bg-gray-300';

                            return (
                                <button
                                    key={item.id}
                                    onClick={() => {
                                        setCurrentIndex(index);
                                        setIsVideoPlaying(false);
                                    }}
                                    className={`w-2 h-2 rounded-full transition-colors ${isActive ? activeColor : inactiveColor
                                        }`}
                                    aria-label={`切换到第 ${index + 1} 个功能`}
                                />
                            );
                        })}
                    </div>

                    <button
                        onClick={handleNext}
                        className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${themeClasses.buttonHover} ${themeClasses.textSecondary}`}
                    >
                        <span>下一个</span>
                        <ChevronRight size={20} />
                    </button>
                </div>
            </div>
        </div>
    );
};

export default WhatsNewModal;
